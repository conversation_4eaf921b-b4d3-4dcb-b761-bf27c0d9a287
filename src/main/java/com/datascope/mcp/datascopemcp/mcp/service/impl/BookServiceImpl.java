package com.datascope.mcp.datascopemcp.mcp.service.impl;

import com.datascope.mcp.datascopemcp.mcp.service.BookService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class BookServiceImpl implements BookService {

    @Override
    @Tool(name = "getBooks", description = "获取所有的书籍信息")
    public List<String> getBooks() {
        return List.of("葵花宝典", "九阴真经", "易筋经");
    }

    @Override
    @Tool(name = "getBookByName", description = "根据书籍名称获取书籍详细信息")
    public String getBookByName(@ToolParam(description = "书籍名称", required = true) String bookName) {
        return "来查询书籍详细信息了啊老弟";
    }
}
