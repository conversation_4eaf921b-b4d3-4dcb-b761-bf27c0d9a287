package com.datascope.mcp.datascopemcp.mcp.config;

import com.datascope.mcp.datascopemcp.mcp.service.BookService;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class McpServerConfig {

    @Bean
    public ToolCallbackProvider weatherTools(BookService bookService) {
        return MethodToolCallbackProvider.builder().toolObjects(bookService).build();
    }
}
